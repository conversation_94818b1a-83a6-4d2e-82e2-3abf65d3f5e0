package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import cn.hutool.json.JSONArray;

/**
 * 案件集体讨论记录文档生成实现类
 *
 * <AUTHOR>
 */
@Service("caseDiscussionRecordDocument")
public class CaseDiscussionRecordDocumentImpl implements DocumentGenerator {

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Autowired
    private IDocumentService documentService;
    @Autowired
    private ICaseInfoService icaseInfoService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);
        
        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，根据环境判断数据类型
        System.out.println(caseId);
        // dev环境使用模拟数据(type=0)，prod环境使用真实数据(type=1)
        int dataType = "prod".equals(activeProfile) ? 1 : 0;
        processedData = getMockData(dataType, caseId);
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "案件集体讨论记录.docx";
    }

    @Override
    public String getDocumentType() {
        return "CASE-DISCUSSION-RECORD";
    }

    public static Map<String, String> getReverseFieldMapping() {
        Map<String, String> mapping = new HashMap<>();

        mapping.put("TLKSSJ", "discussion_start_time");
        mapping.put("SFWZ", "is_no_owner");
        mapping.put("JLXYJ", "conclusive_opinion");
        mapping.put("XYWYBS", "industry_unique_id");
        mapping.put("DZ", "meeting_location");
        mapping.put("CJSJ", "create_time");
        mapping.put("SJSSBMYYTYSJQXCL", "data_dept_permission");
        mapping.put("BZ", "remark");
        mapping.put("KZZD2", "ext_field2");
        mapping.put("FWZXZDTBSJYFWZXSJG", "service_center_sync_field");
        mapping.put("AY", "case_reason");
        mapping.put("SJMC", "city_name");
        mapping.put("TLJSSJ", "discussion_end_time");
        mapping.put("ZCRZW", "moderator_position");
        mapping.put("SJBM", "city_code");
        mapping.put("FZRSHYJ", "legal_review_opinion");
        mapping.put("AJBS", "case_id");
        mapping.put("XTCJSJCXBYDX", "sys_create_time_no_rw");
        mapping.put("XGR", "modifier");
        mapping.put("FWZXZDTBSJYFWZXSJS", "service_center_delete_flag");
        mapping.put("XTGXSJCXBYDX", "sys_update_time_no_rw");
        mapping.put("SFYX", "is_valid");
        mapping.put("JLRZW", "recorder_position");
        mapping.put("CXRYFBYJ", "attendee_opinions");
        mapping.put("JLR", "recorder_name");
        mapping.put("LXRYXMJZW", "observer_names_positions");
        mapping.put("DQZMJ", "tobacco_bureau_name");
        mapping.put("CJR", "creator");
        mapping.put("SFLX", "is_type");
        mapping.put("XGSJ", "modify_time");
        mapping.put("MCRKSJ", "mc_import_time");
        mapping.put("KZZD1", "ext_field1");
        mapping.put("QXRYXMJZW", "absent_names_positions");
        mapping.put("SJSSDWYYTYSJQXCL", "data_unit_permission");
        mapping.put("TLJL", "discussion_record");
        mapping.put("AJBH", "case_number");
        mapping.put("AJJTTLBS", "case_discussion_id");
        mapping.put("ZCR", "moderator_name");
        mapping.put("AJCBRYHBAQJDAJDCLYJ", "case_handler_report");
        mapping.put("KZZD3", "ext_field3");
        mapping.put("CXRYXMJZW", "attendee_names_positions");

        return mapping;
    }

    /**
     * 获取模拟数据
     * @param type 数据类型：0-模拟数据(dev环境)，1-真实数据(prod环境)
     * @param caseId 案件ID
     */
    private Map<String, Object> getMockData(int type, String caseId) {

        Map<String, Object> mockData = new HashMap<>();
        if(type == 1) {
            Map<String, Object> query = new HashMap<>();
            query.put("AJBS", caseId);
            JSONArray array = icaseInfoService.getCaseDiscussionRecordDailyReport(query);

            // 如果array不为空，将第一条数据传给mockData
            if(array != null && array.size() > 0) {
                Map<String, Object> firstData = (Map<String, Object>) array.get(0);
                Map<String, Object> processData = new HashMap<>();
                Map<String, String> mapper = getReverseFieldMapping();
                if(firstData != null) {
                    // 处理数据
                    firstData.forEach((key, value) -> {
                        String newKey = mapper.get(key);
                        if (StrUtil.isBlank(newKey)) {
                            newKey = key;
                        }
                        processData.put(newKey, value);
                    });
                    return processData;
                }
            }
        }

        // 基础信息
        mockData.put("case_discussion_id", "discussion_001");
        mockData.put("case_id", "6358d676d24647f1b824c1f362674299");
        mockData.put("case_number", "博烟案﹝2025﹞第48号");
        mockData.put("case_reason", "未在当地烟草专卖批发企业进货");
        mockData.put("tobacco_bureau_name", "广东省博罗县烟草专卖局");

        // 会议时间和地点
        mockData.put("discussion_start_time", "2025/6/10 9:00");
        mockData.put("discussion_end_time", "2025/6/10 11:30");
        mockData.put("meeting_location", "广东省博罗县烟草专卖局会议室");

        // 时间分解字段
        mockData.put("start_year", "2025");
        mockData.put("start_month", "6");
        mockData.put("start_day", "10");
        mockData.put("start_hour", "9");
        mockData.put("start_minute", "0");
        mockData.put("end_year", "2025");
        mockData.put("end_month", "6");
        mockData.put("end_day", "10");
        mockData.put("end_hour", "11");
        mockData.put("end_minute", "30");

        // 会议人员
        mockData.put("moderator_name", "李明");
        mockData.put("moderator_position", "局长");
        mockData.put("recorder_name", "蔡秋宝");
        mockData.put("recorder_position", "科员");
        mockData.put("attendee_names_positions", "李明（局长）、张华（副局长）、王强（法规科科长）");
        mockData.put("observer_names_positions", "蔡秋宝（科员）、叶辉明（执法员）、朱兆强（执法员）");
        mockData.put("absent_names_positions", "");

        // 会议内容
        mockData.put("case_handler_report", "案件承办人员汇报：经调查，当事人梁俊强经营的博罗县龙溪隆胜轩茶烟酒商行未在当地烟草专卖批发企业进货，现场查获涉案卷烟17个品种1075条，进货总额108625.00元。建议按照《烟草专卖法实施条例》第五十六条规定，处以进货总额9.5%的罚款。");
        
        mockData.put("attendee_opinions", "李明：同意处罚决定；张华：同意处罚决定；王强：同意处罚决定");
        
        mockData.put("discussion_record", "主持人宣布会议开始，介绍案件基本情况。承办人员汇报案件调查情况：当事人梁俊强未在当地烟草专卖批发企业进货，涉案卷烟17个品种1075条，进货总额108625.00元。经讨论，与会人员一致认为事实清楚、证据确凿，同意按照相关法规进行处罚。");
        
        mockData.put("conclusive_opinion", "经集体讨论，一致同意对当事人梁俊强作出行政处罚决定：处以未在当地烟草专卖批发企业进货总额人民币108625.00元的9.5％罚款，计罚款人民币10319.37元。");
        
        mockData.put("legal_review_opinion", "经审核，案件事实清楚，证据确凿，适用法律正确，处罚幅度适当，程序合法，同意承办人员的处理意见。");

        // 文档标题用的烟草专卖局名称（简化版）
        mockData.put("bureau_title", "广东省博罗县烟草专卖局");

        // 系统字段
        mockData.put("is_valid", 1);
        mockData.put("is_no_owner", 0);
        mockData.put("is_type", 1);
        mockData.put("creator", "蔡秋宝");
        mockData.put("create_time", "2025/6/10 17:15");
        mockData.put("modifier", "蔡秋宝");
        mockData.put("modify_time", "2025/6/10 17:15");
        mockData.put("mc_import_time", "2025/6/11 1:03");
        mockData.put("city_code", "10441300");
        mockData.put("city_name", "惠州市");
        mockData.put("data_dept_permission", "4413231030000002829");
        mockData.put("data_unit_permission", "4413231030000000540");
        mockData.put("service_center_sync_field", "");
        mockData.put("service_center_delete_flag", 0);
        mockData.put("industry_unique_id", "");
        mockData.put("remark", "");

        // 扩展字段
        mockData.put("ext_field1", "");
        mockData.put("ext_field2", "");
        mockData.put("ext_field3", "");

        return mockData;
    }
}
